<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theany's Portfolio</title>
    <meta name="description" content="Professional portfolio of Naratheany Vorn">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="particles"></div>

    <div class="loader">
        <div class="loader-spinner"></div>
    </div>

    <header>
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid d-flex align-items-center">
                <!-- Left: Brand/Name -->
                <a class="navbar-brand text-light fw-bold me-3" href="#">
                    <h3 class="mb-0">Theany</h3>
                </a>

                <!-- Mobile Toggler -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Center: Nav Links and Theme Toggle for mobile -->
                <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                        <li class="nav-item"><a class="nav-link" href="#skills">Skills</a></li>
                        <li class="nav-item"><a class="nav-link" href="#education">Education</a></li>
                        <li class="nav-item"><a class="nav-link" href="#projects">Projects</a></li>
                        <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                    </ul>
                    <!-- Theme Toggle Button for mobile (visible only on small screens) -->
                    <button class="theme-toggle d-lg-none ms-3 mt-3 mt-lg-0" aria-label="Toggle theme">
                        <span class="theme-icon">🌙</span>
                    </button>
                </div>

                <!-- Right: Theme Toggle Button for desktop -->
                <button class="theme-toggle d-none d-lg-flex ms-3" aria-label="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </nav>

        <div class="profile">
            <img src="img/profile.jpg" alt="Profile picture" class="profile-img">
            <h1>Naratheany Vorn</h1>
            <div class="contact-links">
                <a href="mailto:<EMAIL>" target="_blank">📧 Email</a>
                <a href="https://github.com/TheanyVorn/" target="_blank">🐙 GitHub</a>
                <a href="https://www.linkedin.com/in/naratheany-vorn-493846341" target="_blank">💼 LinkedIn</a>
                <a href="https://t.me/Vnaratheany" target="_blank">🌐 Telegram</a>
            </div>
        </div>
    </header>

    <main>
        <section id="about" class="section fade-in">
            <h2>About Me</h2>
            <div class="about-content">
                <p>Hi, I'm Naratheany, a thriving, dedicated, and passionate sophomore majoring in Computer Science
                    specialized in Software Engineering at Cambodia Academy of Digital Technology. Currently, I am also
                    pursuing a UX/UI Design and Web Development course at Sister of Code. I am actively working on
                    projects to build my skills in this field. Besides learning hard skills, I am also into social work
                    and I have experience in managing projects as well as leading teams. 🚀</p>
            </div>
        </section>

        <section id="skills" class="section fade-in">
            <h2>My Skills</h2>
            <div class="skills-grid">
                <div class="skill-card">
                    <div class="skill-icon">🌐</div>
                    <div class="skill-name">HTML</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🎨</div>
                    <div class="skill-name">CSS</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">⚡</div>
                    <div class="skill-name">JavaScript</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">☕</div>
                    <div class="skill-name">Java</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">⚙️</div>
                    <div class="skill-name">C++</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">⚛️</div>
                    <div class="skill-name">React</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🚀</div>
                    <div class="skill-name">Node.js</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🐙</div>
                    <div class="skill-name">Git & GitHub</div>
                </div>
            </div>
        </section>
    
        <section id="education" class="section fade-in">
            <h2>My Education</h2>
            <div class="education-timeline">
                <div class="education-item">
                    <div class="education-header">
                        <h5>Bachelor's Degree in Computer Science specializing in Software Engineering</h5> 
                        <span class="date">(2023 - Present)</span>
                    </div>
                    <p>Cambodia Academy of Digital Technology</p>
                </div>
                <div class="education-item">
                    <div class="education-header">
                        <h5>Web Development and UX/UI Design</h5>
                        <span class="date">(May 2025 - Present)</span>
                    </div>
                    <p>Sister of Code</p>
                </div>
                <div class="education-item">
                    <div class="education-header">
                        <h5>Preah Angduong High School</h5>
                        <span class="date">(2017 - 2022)</span>
                    </div>
                    <p>Prey Veng Province</p>
                </div>
                <div class="education-item">
                    <div class="education-header">
                        <h5>Online English Language Tutor</h5>
                        <span class="date">(Mar 2021 - Jul 2021)</span>
                    </div>
                    <p>University Of Otago</p>
                </div>
            </div>
        </section>

        <section id="projects" class="section fade-in">
            <h2>My Projects</h2>
            <div class="projects-grid">
                <article class="project-card">
                    <h3>Porsche Models 🏎️</h3>
                    <p>Showcasing Porsche models with stunning animations using HTML, CSS, and JavaScript. Features
                        interactive galleries and smooth transitions.</p>
                    <a href="https://github.com/TheanyVorn/TheanyVorn-Final-Project-WD---Naratheany-Vorn.git"
                        target="_blank">View Project</a>
                </article>
                <article class="project-card">
                    <h3>Flower Shop 💐</h3>
                    <p>Complete CRUD application for managing a flower shop's inventory. Built with Java, featuring
                        user-friendly interface and database integration.</p>
                    <a href="https://github.com/TheanyVorn/Flower-Shop.git" target="_blank">View Project</a>
                </article>
                <article class="project-card">
                    <h3>Portfolio Site 👩🏻‍💻</h3>
                    <p>Collaborative portfolio project developed in a team of 2 using modern HTML5, CSS3, and
                        JavaScript. Features responsive design and interactive elements.</p>
                    <a href="https://github.com/TheanyVorn/Js-Project---Theany-Taingchhay.git" target="_blank">View
                        Project</a>
                </article>
            </div>
        </section>

        <section id="contact" class="section fade-in">
            <h2>Get in Touch</h2>
            <form id="contact-form" class="contact-form">
                <div class="form-group">
                    <label for="name">Your Name</label>
                    <input type="text" id="name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="email">Your Email</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>

                <button type="submit" class="submit-btn">Send Message</button>
            </form>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 Naratheany Vorn. All Rights Reserved.</p>
    </footer>

    <button class="back-to-top" id="backToTop" aria-label="Back to top">⬆️</button>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>

</html>