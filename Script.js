// Scroll-based fade-in animation
const faders = document.querySelectorAll('.fade-in');
if (faders.length > 0) {
    const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });
    faders.forEach(fade => observer.observe(fade));
}

// Theme toggle
document.querySelectorAll('.theme-toggle').forEach(toggleBtn => {
    toggleBtn.addEventListener('click', () => {
        document.body.classList.toggle('light-mode');
        const icon = toggleBtn.querySelector('.theme-icon');
        if (icon) {
            icon.textContent = document.body.classList.contains('light-mode') ? '☀️' : '🌙';
        }
    });
});

// Back to top
const backToTop = document.getElementById('backToTop');
if (backToTop) {
    window.addEventListener('scroll', () => {
        backToTop.classList.toggle('visible', window.scrollY > 300);
    });
    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
}

// Set default theme to light mode on page load
window.addEventListener('DOMContentLoaded', () => {
    document.body.classList.add('light-mode');
});

// Loader
const loader = document.querySelector('.loader');
if (loader) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            loader.classList.add('loader-hidden');
        }, 2000);
    });
}

// Hamburger Menu Toggle
const hamburgerBtn = document.getElementById("hamburger-btn");
const navMenu = document.getElementById("nav-menu");

if (hamburgerBtn && navMenu) {
    hamburgerBtn.addEventListener("click", () => {
        navMenu.classList.toggle("show");
    });
}

// View Details functionality
function viewDetails(experienceId) {
    // You can implement modal, accordion, or redirect to detail page
    console.log('Viewing details for:', experienceId);
    
    // Example: Open modal
    // showModal(experienceId);
    
    // Example: Expand content
    // toggleExperienceDetails(experienceId);
    
    // Example: Redirect to detail page
    // window.open(`experience-details.html?id=${experienceId}`, '_blank');
}