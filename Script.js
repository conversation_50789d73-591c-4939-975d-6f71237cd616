// Scroll-based fade-in animation
const faders = document.querySelectorAll('.fade-in');
const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');
        }
    });
}, { threshold: 0.1 });
faders.forEach(fade => observer.observe(fade));


// Theme toggle
document.querySelectorAll('.theme-toggle').forEach(toggleBtn => {
    toggleBtn.addEventListener('click', () => {
        document.body.classList.toggle('light-mode');
        const icon = toggleBtn.querySelector('.theme-icon');
        icon.textContent = document.body.classList.contains('light-mode') ? '☀️' : '🌙';
    });
});

// Back to top
const backToTop = document.getElementById('backToTop');
window.addEventListener('scroll', () => {
    backToTop.classList.toggle('visible', window.scrollY > 300);
});
backToTop.addEventListener('click', () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
});

// Set default theme to light mode on page load
window.addEventListener('DOMContentLoaded', () => {
    document.body.classList.add('light-mode');
});

// Loader
window.addEventListener('load', () => {
    setTimeout(() => {
        document.querySelector('.loader').classList.add('loader-hidden');
    }, 2000);
});

// Hamburger Menu Toggle
const hamburgerBtn = document.getElementById("hamburger-btn");
const navMenu = document.getElementById("nav-menu");

hamburgerBtn.addEventListener("click", () => {
    navMenu.classList.toggle("show");
});
